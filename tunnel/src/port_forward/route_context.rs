use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;

use crate::port_forward::route_table::RouteTable;
use tokio::spawn;
use tokio::sync::RwLock;
use tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use tokio::time::sleep;

pub struct RouteContext {
    route_map: Arc<RwLock<HashMap<String, Arc<RouteTable>>>>,
    timeout_job: <PERSON><PERSON><PERSON><PERSON><PERSON><()>,
}

impl RouteContext {
    pub fn new() -> Self {
        let route_map = Arc::new(RwLock::new(HashMap::<String, Arc<RouteTable>>::new()));

        // 启动超时检查任务
        let route_table_map_arc = route_map.clone();
        let timeout_job = spawn(async move {
            loop {
                sleep(Duration::from_secs(60 * 10)).await;
                let mut lock_write_guard = route_table_map_arc.write().await;
                let mut remove_keys = vec![];
                // 检查并收集超时的路由
                for (key, route_table) in lock_write_guard.iter() {
                    if route_table.is_timeout().await {
                        remove_keys.push(key.clone());
                    }
                }
                // 移除超时的路由并关闭连接
                for key in remove_keys {
                    if let Some(route_table) = lock_write_guard.remove(&key) {
                        route_table.remove_all().await;
                    }
                }
                drop(lock_write_guard);
            }
        });

        RouteContext {
            route_map,
            timeout_job,
        }
    }

    /// 查询并新建
    pub async fn get_or_create(&self, uuid: &String) -> Arc<RouteTable> {
        let mut write_guard = self.route_map.write().await;
        if let Some(route_table) = write_guard.get(uuid) {
            route_table.clone()
        } else {
            let route_table_arc = Arc::new(RouteTable::new());
            write_guard.insert(uuid.clone(), route_table_arc.clone());
            route_table_arc
        }
    }
}