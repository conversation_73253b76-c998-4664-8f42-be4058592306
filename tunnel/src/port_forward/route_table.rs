use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
pub use tokio::io::AsyncReadExt;
use tokio::spawn;
use tokio::sync::RwLock;
use tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use tokio::time::{sleep, Instant};

use crate::port_forward::router::Route;
use flyshadow_common::tunnel::tunnel_package::PackageProtocol;

// 路由表结构体，包含TCP和UDP路由映射以及超时任务句柄
pub struct RouteTable {
    tcp_source_addr_route_map: Arc<RwLock<HashMap<String, Arc<Route>>>>,
    udp_target_addr_route_map: Arc<RwLock<HashMap<String, Arc<Route>>>>,
    active_time: Arc<RwLock<Instant>>,
    timeout_job: JoinHandle<()>,
}

impl RouteTable {
    /// 创建新的路由表实例
    pub fn new() -> RouteTable {
        let tcp_source_addr_route_map = Arc::new(RwLock::new(HashMap::<String, Arc<Route>>::new()));
        let udp_target_addr_route_map = Arc::new(RwLock::new(HashMap::<String, Arc<Route>>::new()));

        let route_map_clone = udp_target_addr_route_map.clone();

        // 启动超时检查任务
        let timeout_job = spawn(async move {
            loop {
                sleep(Duration::from_secs(60)).await;
                let mut lock_write_guard = route_map_clone.write().await;
                let mut remove_keys = vec![];
                // 检查并收集超时的路由
                for (key, route) in lock_write_guard.iter() {
                    if route.is_timeout().await {
                        remove_keys.push(key.clone());
                    }
                }
                // 移除超时的路由并关闭连接
                for key in remove_keys {
                    if let Some(route) = lock_write_guard.remove(&key) {
                        route.close().await;
                    }
                }
                drop(lock_write_guard);
            }
        });

        RouteTable {
            tcp_source_addr_route_map,
            udp_target_addr_route_map,
            active_time: Arc::new(RwLock::new(Instant::now())),
            timeout_job,
        }
    }

    /// 向目标发送数据
    pub async fn send_data_to_target(&self, source_addr: String, target_addr: String, package_protocol: PackageProtocol, data: Vec<u8>) -> bool {
        *self.active_time.write().await = Instant::now();
        match package_protocol {
            PackageProtocol::TCP => {
                // 构建TCP路由键并发送数据
                let key = format!("{}==={}==={:?}", source_addr, target_addr, package_protocol);
                if let Some(route) = self.tcp_source_addr_route_map.read().await.get(&key) {
                    route.send_tcp_data_to_target(data).await;
                    true
                } else { false }
            }
            PackageProtocol::UDP => {
                // 构建UDP路由键并发送数据
                let key = format!("{}==={:?}", source_addr, package_protocol);
                if let Some(route) = self.udp_target_addr_route_map.read().await.get(&key) {
                    route.send_udp_data_to_target(target_addr, data).await;
                    true
                } else { false }
            }
            _ => true,
        }
    }

    /// 添加新的路由
    pub async fn add_route(&self, route: Arc<Route>) {
        *self.active_time.write().await = Instant::now();
        match route.protocol {
            PackageProtocol::TCP => {
                let key = format!("{}==={}==={:?}", route.source_addr, route.target_addr, route.protocol);
                self.tcp_source_addr_route_map.write().await.insert(key, route);
            }
            PackageProtocol::UDP => {
                let key = format!("{}==={:?}", route.source_addr, route.protocol);
                self.udp_target_addr_route_map.write().await.insert(key, route);
            }
            _ => {}
        }
    }

    /// 是否存在路由
    pub async fn exist_route(&self, source_addr: &String, target_addr: &String, package_protocol: &PackageProtocol) -> bool {
        *self.active_time.write().await = Instant::now();
        match package_protocol {
            PackageProtocol::TCP => {
                let key = format!("{}==={}==={:?}", source_addr, target_addr, package_protocol);
                self.tcp_source_addr_route_map.write().await.contains_key(&key)
            }
            PackageProtocol::UDP => {
                let key = format!("{}==={:?}", source_addr, package_protocol);
                self.udp_target_addr_route_map.write().await.contains_key(&key)
            }
            _ => {
                false
            }
        }
    }

    /// 移除指定路由
    pub async fn remove_route(&self, source_addr: &String, target_addr: &String, package_protocol: PackageProtocol) {
        *self.active_time.write().await = Instant::now();
        let key = format!("{}==={}==={:?}", source_addr, target_addr, package_protocol);
        if let Some(route) = self.tcp_source_addr_route_map.write().await.remove(&key) {
            route.disconnect().await;
        }
    }

    /// 移除所有路由并关闭所有连接
    pub async fn remove_all(&self) {
        for route in self.tcp_source_addr_route_map.write().await.drain() {
            route.1.close().await;
        }
        for route in self.udp_target_addr_route_map.write().await.drain() {
            route.1.close().await;
        }
        self.timeout_job.abort();
    }

    /// 判断超时 检查此路由表是否无操作
    pub async fn is_timeout(&self) -> bool {
        let now = Instant::now();
        now.duration_since(*self.active_time.read().await) > Duration::from_secs(60 * 10)
    }
}
