use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;

use tokio::spawn;
use tokio::sync::RwLock;
use tokio::task::JoinHandle;
use tokio::time::sleep;

use crate::route_table::route_table::RouteTable;

/// 路由上下文管理器
///
/// 该结构体负责管理多个路由表实例，提供路由表的创建、查询、删除等功能。
/// 主要特性：
/// - 基于UUID的路由表管理
/// - 自动超时检测和清理机制
/// - 线程安全的并发访问
/// - 资源自动回收
pub struct RouteContext {
    /// 路由表映射：UUID -> RouteTable
    /// 使用Arc<RwLock<>>确保多线程环境下的安全访问
    route_map: Arc<RwLock<HashMap<String, Arc<RouteTable>>>>,

    /// 后台超时检查任务句柄
    /// 定期检查并清理超时的路由表，防止内存泄漏
    timeout_job: JoinHandle<()>,
}

impl RouteContext {
    /// 创建新的路由上下文实例
    ///
    /// 该方法会初始化路由表映射并启动后台超时检查任务。
    /// 超时检查任务每10分钟运行一次，自动清理超时的路由表。
    ///
    /// # 返回值
    /// 返回一个新的RouteContext实例
    pub fn new() -> Self {
        // 初始化路由表映射，使用Arc<RwLock<>>确保线程安全
        let route_map = Arc::new(RwLock::new(HashMap::<String, Arc<RouteTable>>::new()));

        // 克隆路由表映射的引用，用于后台超时检查任务
        let route_table_map_arc = route_map.clone();

        // 启动后台超时检查任务
        // 该任务每10分钟运行一次，检查并清理超时的路由表
        let timeout_job = spawn(async move {
            loop {
                // 等待10分钟后进行下一次检查
                sleep(Duration::from_secs(60 * 10)).await;

                // 分两步处理：先收集超时的路由表，再移除它们，减少锁持有时间
                let timeout_route_tables = {
                    let lock_read_guard = route_table_map_arc.read().await;
                    let mut timeout_route_tables = Vec::new();

                    // 遍历所有路由表，检查是否超时
                    for (key, route_table) in lock_read_guard.iter() {
                        if route_table.is_timeout().await {
                            // 收集需要移除的超时路由表
                            timeout_route_tables.push((key.clone(), route_table.clone()));
                        }
                    }
                    timeout_route_tables
                };

                // 如果有超时的路由表，获取写锁并移除它们
                if !timeout_route_tables.is_empty() {
                    for (key, route_table) in timeout_route_tables {
                        let mut lock_write_guard = route_table_map_arc.write().await;
                        // 再次检查路由表是否仍在映射中（避免重复处理）
                        if lock_write_guard.contains_key(&key) {
                            lock_write_guard.remove(&key);
                            // 在释放写锁后关闭路由表中的所有连接
                            drop(lock_write_guard);
                            route_table.remove_all().await;
                        }
                    }
                }
            }
        });

        RouteContext {
            route_map,
            timeout_job,
        }
    }

    /// 获取或创建路由表
    ///
    /// 根据提供的UUID查找对应的路由表，如果不存在则创建一个新的路由表。
    /// 该方法是线程安全的，支持并发访问。
    ///
    /// # 参数
    /// - `uuid`: 路由表的唯一标识符
    ///
    /// # 返回值
    /// 返回对应UUID的路由表实例（Arc包装）
    ///
    /// # 性能优化
    /// 首先尝试使用读锁进行查找，只有在需要创建新路由表时才使用写锁，
    /// 这样可以提高并发性能，减少锁竞争
    pub async fn get_or_create(&self, uuid: &str) -> Arc<RouteTable> {
        // 首先尝试使用读锁查找现有的路由表
        {
            let read_guard = self.route_map.read().await;
            if let Some(route_table) = read_guard.get(uuid) {
                // 如果找到，直接返回克隆的引用
                return route_table.clone();
            }
            // 读锁在此处自动释放
        }

        // 如果没有找到，获取写锁来创建新的路由表
        let mut write_guard = self.route_map.write().await;

        // 双重检查：在获取写锁期间，可能其他线程已经创建了该路由表
        if let Some(route_table) = write_guard.get(uuid) {
            // 如果其他线程已经创建，直接返回
            route_table.clone()
        } else {
            // 创建新的路由表实例
            let route_table_arc = Arc::new(RouteTable::new());
            // 将新创建的路由表插入到映射中
            write_guard.insert(uuid.to_string(), route_table_arc.clone());
            route_table_arc
        }
    }

    /// 删除指定的路由表
    ///
    /// 根据UUID删除对应的路由表，并关闭该路由表中的所有连接。
    /// 该操作会立即释放相关资源，包括网络连接和后台任务。
    ///
    /// # 参数
    /// - `uuid`: 要删除的路由表的唯一标识符
    ///
    /// # 返回值
    /// - `true`: 成功删除了路由表
    /// - `false`: 指定UUID的路由表不存在
    ///
    /// # 注意事项
    /// 删除操作是不可逆的，删除后该UUID对应的所有连接都会被关闭
    pub async fn remove(&self, uuid: &str) {
        // 获取路由表映射的写锁
        let mut write_guard = self.route_map.write().await;

        // 从映射中移除指定UUID的路由表
        if let Some(route_table) = write_guard.remove(uuid) {
            // 关闭路由表中的所有连接并清理资源
            route_table.remove_all().await;
        }
    }

}

impl Drop for RouteContext {
    /// 析构函数，确保在RouteContext被销毁时正确清理资源
    ///
    /// 该方法会中止后台超时检查任务，防止任务继续运行导致的资源泄漏。
    /// 注意：由于Drop trait的限制，这里无法异步清理路由表，
    /// 建议在销毁RouteContext之前手动调用清理方法。
    fn drop(&mut self) {
        // 中止后台超时检查任务
        self.timeout_job.abort();
    }
}