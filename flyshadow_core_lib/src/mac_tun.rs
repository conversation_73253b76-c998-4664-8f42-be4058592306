use std::ffi::CString;
use std::io::Error;
use std::mem::forget;
use std::os::raw::c_char;
use std::path::Path;
use std::process::Command;
use std::sync::Arc;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::UnixListener;
use tokio::runtime::Runtime;
use tokio::spawn;
use tokio::sync::RwLock;
use tokio::task::Jo<PERSON><PERSON><PERSON><PERSON>;
use tunnel::context::context::TunnelContext;
use tunnel::tun::tun::Tun;

struct TunServerEnv {
    runtime: Runtime,
    tunnel_context: Arc<TunnelContext>,
    tun: Arc<Tun>,
    job_handle: Arc<RwLock<Vec<JoinHandle<()>>>>,
}

impl TunServerEnv {
    pub fn new(tunnel_context: Arc<TunnelContext>) -> TunServerEnv {
        let runtime = tokio::runtime::Builder::new_multi_thread()
            .enable_all()
            .build()
            .unwrap();

        let context = tunnel_context.clone();
        let tun = runtime.block_on(async move { Arc::new(Tun::new(context).await) });

        TunServerEnv {
            runtime,
            tunnel_context,
            tun,
            job_handle: Arc::new(RwLock::new(Vec::new())),
        }
    }

    pub fn start_tun_server(&self) -> Result<(), Error> {
        let tunnel_context = self.tunnel_context.clone();
        self.runtime.block_on(async {
            if let Ok(interface_selector) = InterfaceSelector::select_iface(IfaceSelectType::IP, tunnel_context.get_ipv6_enable().await).await {
                tunnel_context.set_local_interface(interface_selector).await;
            }

            let path = "/tmp/flyshadow_mac_tun.sock";

            // 清理旧 socket 文件
            if Path::new(path).exists() {
                tokio::fs::remove_file(path).await?;
            }

            let listener = UnixListener::bind(path)?;

            let tun = self.tun.clone();

            let job_handle2 = self.job_handle.clone();

            self.job_handle.write().await.push(spawn(async move {
                loop {
                    let (socket, addr) = if let Ok((socket, addr)) = listener.accept().await {
                        (socket, addr)
                    } else {
                        return;
                    };
                    let (mut read_half, mut write_half) = tokio::io::split(socket);
                    println!("New client connected: {:?}", addr);

                    let tun = tun.clone();
                    let tun1 = tun.clone();

                    job_handle2.write().await.push(spawn(async move {
                        loop {
                            let mut data = tun.get_tun_data().await;
                            encrypt(&mut data);
                            if let Err(e) = write_half.write_u32(data.len() as u32).await {
                                eprintln!("Failed to write header: {}", e);
                                break;
                            }
                            if let Err(e) = write_half.write_all(&data).await {
                                eprintln!("Failed to write to client: {}", e);
                                break;
                            }
                        }
                    }));
                    job_handle2.write().await.push(spawn(async move {
                        loop {
                            // 读取 4 字节长度头
                            let packet_len = match read_half.read_u32().await {
                                Ok(packet_len) => packet_len as usize,
                                Err(e) => {
                                    eprintln!("Failed to read header: {}", e);
                                    break;
                                }
                            };

                            let mut packet_buf = vec![0u8; packet_len];
                            if let Err(e) = read_half.read_exact(&mut packet_buf).await {
                                eprintln!("Failed to read full packet: {}", e);
                                break;
                            }

                            decrypt(&mut packet_buf);
                            tun1.handler_tun_data(packet_buf).await;
                        }
                    }));
                }
            }));

            Ok(())
        })
    }

    pub fn stop_tun_server(&self) {
        self.runtime.block_on(async {
            self.job_handle.write().await.iter().for_each(|h| h.abort());
            self.job_handle.write().await.clear();
        });
        Self::flush_dns_cache()
    }


    /// 刷新 DNS 缓存
    fn flush_dns_cache() {
        let _ = Command::new("dscacheutil")
            .args(&["-flushcache"])
            .status()
            .is_ok();
    }
}

use chacha20::cipher::{KeyIvInit, StreamCipher};
use chacha20::ChaCha20;
use flyshadow_common::interface::interface_selector::{IfaceSelectType, InterfaceSelector};

fn encrypt(data: &mut [u8]) {
    let mut cipher = ChaCha20::new(b"01234567890123456789012345678901".into(), b"flyshadow123".into());
    cipher.apply_keystream(data);
}

fn decrypt(data: &mut [u8]) {
    encrypt(data); // ChaCha20 对称，同样的函数
}


#[unsafe(no_mangle)]
pub extern "C" fn new_tun_server_env(context_ptr: i64) -> i64 {
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };

    let env = TunServerEnv::new(tc.as_ref().clone());

    forget(tc);

    Box::into_raw(Box::new(env)) as i64
}

#[unsafe(no_mangle)]
pub extern "C" fn start_tun_server(env_ptr: i64) -> *mut c_char {
    let env = unsafe { Box::from_raw(env_ptr as *mut TunServerEnv) };

    let r = match env.start_tun_server() {
        Ok(_) => CString::new("").unwrap().into_raw(),
        Err(e) => CString::new(e.to_string()).unwrap().into_raw(),
    };

    forget(env);
    r
}

#[unsafe(no_mangle)]
pub extern "C" fn stop_tun_server(env_ptr: i64) {
    let env = unsafe { Box::from_raw(env_ptr as *mut TunServerEnv) };

    env.stop_tun_server();

    forget(env);
}
